import { observer } from "mobx-react-lite"
import React, { FC, useEffect, useRef, useState } from "react"
import { AppStackScreenProps } from "../../navigators"
import { Screen, Text } from "../../components"
import { Mheader } from "../../navigators/Mheader"
import { FlatList, Image, ImageStyle, Platform, TextStyle, TouchableOpacity, View, ViewStyle, Linking, Pressable } from "react-native"
import { Dimen } from "../../theme/dimen"
import { AI_CENTER, AI_END, COLUMN, FLEX, FLEX_2, JC_CENTER, MARGIN_DF, MARGIN_DF_LEFT, MARGIN_DF_RIGHT, MARGIN_DF_TOP, MARGIN_S_DF_LEFT, MARGIN_S_DF_RIGHT, ROW, ROW_CENTER } from "../../theme/mStyle"
import { CardPrices } from "./CardPrices"
import { BG_GRAY, BT<PERSON>_CONTAINER, CONTAINER_BORDER, G<PERSON><PERSON>_BORDER, TEXT_SECTION, TEXT_SMALL } from "../../theme/baseStyle"
import { MIcon } from "../../components/MIcon"
import { translate } from "../../i18n"
import { CardNews } from "./CardNews"
import Carousel from 'react-native-reanimated-carousel';
import { colors, typography } from "app/theme"
import { getAllPosts, getBanners, getCalendarSinoSound, getMarketNews, getPopups } from "app/api/model"
import { Helper } from "app/utils/helper"
import moment from "moment"
import { AppStorage } from "app/utils/appStorage"
import { useIsFocused, useNavigationState } from "@react-navigation/native"
import { RefreshStorage } from "app/utils/RefreshStorage"
import { Api } from "app/api/api"
import i18n from "i18n-js"
import { BtnContactComp } from "./BtnContactComp"
import RunningText from "app/components/RunningText"
import { AppLinkStorage } from "app/utils/appLinkStorage"
import { useNetInfo } from "@react-native-community/netinfo";
import 'moment/locale/zh-hk';
import 'moment/locale/zh-cn';
import { GestureDetector, Gesture } from 'react-native-gesture-handler'
import { runOnJS } from 'react-native-reanimated'



interface HomeScreenProps extends AppStackScreenProps<"Home"> { }

const HomeBannerCont: ViewStyle = {
  width: Dimen.screenWidth,
  height: Dimen.screenWidth * (690 / 1125),
}

const HomeBannerImg: ImageStyle = {
  width: Dimen.screenWidth,
  height: Dimen.screenWidth * (690 / 1125),
}

const NoticeContainer: ViewStyle = {
  ...CONTAINER_BORDER,
  ...ROW,
  alignItems: 'center',
  width: Dimen.screenWidth - 2 * Dimen.padding.base,
  height: 37,
  marginHorizontal: Dimen.padding.base,
  marginVertical: Dimen.padding.base,
}

const RunningTextStyle: TextStyle = {
  fontSize: Platform.OS === "ios" ? 13 : 12,
  color: "#191919",
  marginStart: Dimen.padding.ssm,
  fontFamily: typography.primary.normal
}

const IndicatorContainer: ViewStyle = {
  ...ROW,
  ...AI_CENTER,
  ...JC_CENTER,
  width: Dimen.screenWidth - 2 * Dimen.padding.base,
  marginHorizontal: Dimen.padding.base,
  paddingVertical: Dimen.padding.ssm,
  marginTop: -50
}

const IndicationItem: ViewStyle = {
  width: 8,
  height: 8,
  borderRadius: 4,
  backgroundColor: colors.mine.textGray,
  borderColor: colors.mine.textGray,
  borderWidth: 1,
  marginHorizontal: 4,

}
const IndicationItemActive: ViewStyle = {
  width: 28,
  height: 8,
  borderRadius: 15,
  backgroundColor: colors.palette.white,
  borderColor: colors.mine.textGray,
  borderWidth: 1,
  marginHorizontal: 4,
}

const TextDate: TextStyle = {
  fontSize: Dimen.fontSize.xl,
  lineHeight: Dimen.lineHeight.md,
  fontWeight: Platform.OS === 'ios' ? '900' : '600',
  color: colors.mine.white,
}

const TextMonth: TextStyle = {
  ...TEXT_SMALL,
  marginStart: 0,
  lineHeight: Dimen.lineHeight.base,
  marginTop: -4,
  marginBottom: 0,
  color: colors.mine.white
}

export const HomeScreen: FC<HomeScreenProps> = observer(function HomeScreen(_props) {
  const { navigation } = _props
  const [bannerList, setBannerList] = useState([])
  const [bannerInfoList, setBannerInfoList] = useState([])
  const [bannerIndex, setBannerIndex] = useState(0)
  const [totalMarketNews, setTotalMarketNews] = useState(0)
  const [totalcalendarEvent, setTotalCalendarEvent] = useState(0)
  const [notifications, setNotifications] = useState([]) // [{id: 1, title: '2023年09月27日:溫馨提示:重大消息或資料公佈前後之'},{id: 2, title: '2023年09月27日:溫馨提示:重大消息或資料公佈前後之'}
  const [notiDisplay, setNotiDisplay] = useState([])
  const [posts, setPosts] = useState([
    { id: -2 }, { id: -1 }, { id: 0 }
  ])
  const [refreshing, setRefreshing] = useState(false)
  const [isHeaderSticked, setIsHeaderSticked] = useState(false)
  const [mainScrollEnable, setMainScrollEnable] = useState(true)
  const bannerIndexRef = useRef(0)
  const currentPage = useRef(1)
  const hasNext = useRef(false)
  const reloadNewCount = useRef(null)
  const reloadCalendarCount = useRef(null)
  const updateIdTimerRef = useRef(null)


  const updatePublicIp = async (callback) => {

    AppStorage.updateIp("0.0.0.0");
    callback();
  }


  const loadBanners = () => {
    getBanners(
      Api.getUrl(),
      (data: any) => {
        const banners = []
        data.results.map((item) => {
          banners.push(item.medium.s3ObjKey.Location)
        })
        console.log("banners:", JSON.stringify(banners))
        setBannerList(banners)
        setBannerInfoList(JSON.parse(JSON.stringify(data.results)) || [])
      },
      (error: any) => {
        console.log('error:', error)
      }
    )
  }

  const loadPosts = (isLoadMore = false) => {
    if (!AppStorage.isNetworkConnected()) {
      setRefreshing(false)
      return
    }
    if (isLoadMore && hasNext.current) {
      currentPage.current += 1
    } else {
      currentPage.current = 1
    }
    getAllPosts(
      0,
      currentPage.current,
      Api.feedPerPageInHome,
      3,
      (data) => {
        setRefreshing(false)
        hasNext.current = data.hasNext
        if (isLoadMore) {
          const dataList = posts
          data.results.forEach((item) => {
            dataList.push(item)
          })
          setPosts(JSON.parse(JSON.stringify(dataList)))
        } else {
          const dataList = []
          dataList.push({ id: -2 })
          dataList.push({ id: -1 })
          dataList.push({ id: 0 })
          data.results.map((item) => {
            dataList.push(item)
          })
          setPosts(() => dataList)
        }
      },
      (error) => {
        setRefreshing(false)
        if (posts.length === 0) {
          setPosts([{ id: 0 }])
        }
        console.log('error:', error)
      }
    )
    getAllPosts(
      4,
      1,
      1,
      3,
      (data) => {
        setNotifications(data.results)
        const dataList = []
        data.results.map((item, index) => {
          console.log("notification item", index)
          
          let notificationString = ""
          if (index == 0) {
            const dateValue = Helper.getValue(item, "createdAt") ? Helper.getValue(item, "createdAt") : Helper.getValue(item, "updatedAt")
            
            if (i18n.locale == 'en') {
              notificationString = moment(dateValue).format(Helper.dateFormateList.dateApi)
            } else {
              notificationString = moment(dateValue).get('year') + translate("common.year") + moment(dateValue).format("M") + translate("common.month") + moment(dateValue).get('date') + translate("common.day") + " "
            }
          }
          
          dataList.push({
            id: item.id,
            content: index == 0 ? notificationString + ": " + Helper.getValue(item, "subTitle") : Helper.getValue(item, "subTitle")
          })
        })
        setNotiDisplay(() => dataList)
      },
      (error) => {
        console.log('error:', error)
      }
    )
  }

  const loadMarketNews = () => {
    getMarketNews(
      1,
      moment().format(Helper.dateFormateList.dateApi),
      (data) => {
        console.log("loadMarketNews: ", data)
        setTotalMarketNews(data.data.total)
        if (reloadNewCount.current) {
          clearTimeout(reloadNewCount.current)
        }
      },
      (error) => {
        setTotalMarketNews(0)
        console.log('error loadMarketNews:', error)
        if (reloadNewCount.current) {
          clearTimeout(reloadNewCount.current)
        }
        reloadNewCount.current = setTimeout(() => {
          console.log("reload market news")
          loadMarketNews()
        }, Api.reloadNewsCalendarNumberInHome * 1000)
      },
      false
    )
  }

  const loadCalendar = () => {
    getCalendarSinoSound(
      moment().format(Helper.dateFormateList.dateApi),
      (data) => {
        setTotalCalendarEvent(data.data.total)
        if (reloadCalendarCount.current) {
          clearTimeout(reloadCalendarCount.current)
        }
      },
      (error) => {
        console.log('error:', error)
        if (reloadCalendarCount.current) {
          clearTimeout(reloadCalendarCount.current)
        }
        reloadCalendarCount.current = setTimeout(() => {
          console.log("reload calendar")
          loadCalendar()
        }, Api.reloadNewsCalendarNumberInHome * 1000)
      },
      false
    )
  }

  const loadPopup = () => {
    getPopups(
      Api.getUrl(),
      (data: any) => {
        // check should show popup
        if (data && data.results && data.results.length > 0) {
          let shouldShowPopup = true
          const pData = data.results[0]
          if (pData.imageOrText != null && pData.imageOrText != undefined) {
            if (pData.imageOrText == 1) {
              console.log(Helper.getValue(pData, "content"))
              if (Helper.getValue(pData, "content") == "") {
                shouldShowPopup = false
              }
            } else {
              console.log(pData.medium.s3ObjKey.Location)
              if (pData.medium
                && pData.medium.s3ObjKey
                && pData.medium.s3ObjKey.Location
                && pData.medium.s3ObjKey.Location != "") {
                shouldShowPopup = true
              } else {
                shouldShowPopup = false
              }
            }
          } else {
            shouldShowPopup = false
          }
          console.log("popupData: ", pData)
          console.log("shouldShowPopup: ", shouldShowPopup)
          console.log("currentScreen: ", AppStorage.appStorage.currentScreen)
          if (shouldShowPopup && AppStorage.appStorage.currentScreen == "Home" && AppStorage.appStorage.popupShownCount == 0) {
            AppStorage.updatePopupShownCount()
            AppStorage.setPopupDataAndShow(pData)
          }
        }
      },
      (error) => {
        console.log('error:', error)
      }
    )
  }

  const onBannerClicked = (bannerPos) => {
    if (!AppStorage.isNetworkConnected()) {
      return
    }
    console.log("onBannerClicked: ", bannerPos)
    if (bannerInfoList.length > 0 && bannerPos < bannerInfoList.length) {
      const banner = bannerInfoList[bannerPos]
      if (banner && banner.url && banner.url != "") {
        if (banner.externalOrInternal == 0) {
          // deep link
          AppLinkStorage.handleDeepLinkInsideApp(banner.url, navigation)
        } else {
          // url
          Linking.openURL(banner.url)
        }
      }
    }
  }

  const prepareData = () => {
    AppStorage.hideMessage()
    loadBanners()
    loadPosts()
    loadMarketNews()
    loadCalendar()
    loadPopup()
  }

  const netInfo = useNetInfo()
  const saveConnectionState = useRef(null)
  useEffect(() => {
    console.log("netInfo.isConnected: ", netInfo.isConnected)
    AppStorage.updateNetworkStatus(netInfo.isConnected)
    if (saveConnectionState.current == null) {
      saveConnectionState.current = netInfo.isConnected
    } else {
      if (netInfo.isConnected) {
        prepareData()
      }
    }
  }, [netInfo.isConnected])

  useEffect(() => {
    moment.updateLocale('zh-hk', {
      months: Helper.getMonthNames()
    })
    updatePublicIp(() => {
      if (updateIdTimerRef.current == null) {
        updateIdTimerRef.current = setInterval(() => {
          updatePublicIp(() => {
            console.log("reload public ip")
          })
        }, Api.reloadIpTime)
      }
      prepareData()
      AppLinkStorage.setNavigation(navigation)

      return () => {
        if (updateIdTimerRef.current) {
          clearInterval(updateIdTimerRef.current)
          updateIdTimerRef.current = null
        }
      }
    })
  }, [])

  // start of count down to refresh feed
  // remember call reset timeout when user reload list
  const isScreenShown = useIsFocused()
  const timeoutRef = useRef(null)
  const resetTimeout = (delayTime = Api.reloadTime) => {
    console.log("resetTimeout: " + delayTime)
    if (timeoutRef.current) {
      clearTimeout(timeoutRef.current)
    }
    RefreshStorage.updateScreenSetTime("Home")
    timeoutRef.current = setTimeout(() => {
      console.log("reload posts")
      setRefreshing(true)
      loadPosts()
      resetTimeout()
    }, delayTime * 1000)
  }

  useEffect(() => {
    console.log("LatestNewsScreen isScreenShown: ", isScreenShown)
    if (isScreenShown) {
      if (RefreshStorage.screenNameAdded("Home")) {
        if (RefreshStorage.shouldRefreshScreen("Home")) {
          loadPosts()
          resetTimeout()
        } else {
          if (timeoutRef.current) {
            clearTimeout(timeoutRef.current)
          }
          resetTimeout(RefreshStorage.getDiffSeconds("Home"))
        }
      } else {
        resetTimeout()
      }

      if (!AppStorage.appStorage.notificationPromptShown) {
        setTimeout(() => {
          AppStorage.setShowNotificationPrompt(true);
        }, 200);
      }
    }
  }, [isScreenShown])
  // end of count down to refresh feed

  const renderActionButton = () => {
    if (!Helper.isLoggeIn()) {
      return (<Text
        onPress={() => {
          navigation.navigate("Login")
        }}
        style={BTN_CONTAINER}
        tx="homePage.createAccount" />)
    } else {
      if (Helper.isPortalUser(AppStorage.appStorage.userDevice)) {
        return (
          <View style={ROW_CENTER}>
            <Text
              onPress={() => {
                navigation.navigate("DemoAccountRegistration")
              }}
              numberOfLines={1}
              style={[BTN_CONTAINER, BG_GRAY, FLEX, MARGIN_S_DF_RIGHT, { paddingHorizontal: Dimen.padding.ssm }]}
              tx="homePage.applyDemoAccount"
            />
            <Text
              onPress={() => {
                //navigation.navigate("RealAccountRegistration")
                Helper.loadRegLinkSinoSound(
                  _props.navigation,
                  AppStorage.appStorage.userDevice?.areaCode || "",
                  AppStorage.appStorage.userDevice?.mobile || ""
                )
              }}
              numberOfLines={1}
              style={[BTN_CONTAINER, FLEX, MARGIN_S_DF_LEFT, { paddingHorizontal: Dimen.padding.ssm }]}
              tx="homePage.applyLiveAccount"
            />
          </View>
        )
      } else if (Helper.isDemoUser(AppStorage.appStorage.userDevice)) {
        return (
          <Text
            onPress={() => {
              //navigation.navigate("RealAccountRegistration")
              Helper.loadRegLinkSinoSound(
                _props.navigation,
                AppStorage.appStorage.userDevice?.areaCode || "",
                AppStorage.appStorage.userDevice?.mobile || ""
              )
            }}
            style={[BTN_CONTAINER, FLEX]}
            tx="homePage.applyLiveAccount"
          />
        )
      } else if (Helper.isLiveAccount(AppStorage.appStorage.userDevice)) {
        return (
          <View style={ROW_CENTER}>
            <Text
              onPress={() => {
                // navigation.navigate("Webview")
                if (!AppStorage.isNetworkConnected()) {
                  return
                }
                //Helper.loadCsUrlAndGo(navigation)
                Helper.loadWebViewLinkSinoSound(
                  _props.navigation,
                  AppStorage.appStorage.userDevice.clientId,
                  'deposit'
                )
              }}
              style={[BTN_CONTAINER, FLEX]}
              tx="account.eject"
            />
            <Text
              onPress={() => {
                // navigation.navigate("Webview")
                if (!AppStorage.isNetworkConnected()) {
                  return
                }
                //Helper.loadCsUrlAndGo(navigation)
                Helper.loadWebViewLinkSinoSound(
                  _props.navigation,
                  AppStorage.appStorage.userDevice.clientId,
                  'withdrawal'
                )
              }}
              style={[BTN_CONTAINER, FLEX]}
              tx="account.withdraw"
            />
          </View>
        )
      } else {
        return (<Text style={BTN_CONTAINER} tx="homePage.createAccount" />)
      }
    }
  }

  const updateBannerIndex = function () {
    if (bannerIndexRef.current >= bannerList.length - 1) {
      bannerIndexRef.current = 0
    } else {
      bannerIndexRef.current = bannerIndexRef.current + 1
    }
    setBannerIndex(bannerIndexRef.current)
  }

  const getFakeStickyHeight = () => {
    return Dimen.screenWidth * (690 / 1125) - 30
  }

  const delayedEnableScroll = () => {
    setTimeout(() => {
      setMainScrollEnable(true)
    }, 500)
  }

  const panGestureHandler = Gesture.Pan()
    .onBegin(() => {
      console.log("onTouchBegin")
      runOnJS(setMainScrollEnable)(false)
    })
    .onEnd(() => {
      console.log("onTouchEnd")
    })
    .onTouchesUp(() => {
      console.log("onTouchesUp")
    })    
    .onFinalize(() => {
      console.log("onTouchFinalize")
      runOnJS(delayedEnableScroll)()
    })
  
  
  const renderStickyLevel1 = () => {
    return (
      <View key="renderTopItemOfList">
        <GestureDetector gesture={panGestureHandler}>
          {<Carousel
            loop
            width={Dimen.screenWidth}
            height={Dimen.screenWidth * (690 / 1125)}
            autoPlay={true}
            data={bannerList}
            scrollAnimationDuration={2000}
            autoPlayInterval={4000}
            pagingEnabled={true}
            snapEnabled={true}
            overscrollEnabled={false}
            onSnapToItem={(index) => {
              console.log('onSnapToItem index:', index)
              setMainScrollEnable(true)
            }}
            onScrollBegin={() => {
              updateBannerIndex()           
            }}            
            onScrollEnd={(index) => {
              bannerIndexRef.current = index
              setBannerIndex(index)
            }}
            renderItem={({ item, index }) => (
              <TouchableOpacity                
                onPress={() => {
                  onBannerClicked(index)
                }}
                activeOpacity={1}
                key={index}
                style={HomeBannerCont} >
                <Image source={{ uri: item }} resizeMode="contain" style={HomeBannerImg} />
              </TouchableOpacity>              
            )}
          />}
        </GestureDetector>
        <View style={IndicatorContainer}>
          {
            bannerList.map((item, index) => {
              return (
                <View key={index} style={bannerIndex === index ? IndicationItemActive : IndicationItem} />
              )
            })
          }
        </View>
      </View>
    )
  }

  const renderStickyLevel2 = () => {
    return (
      <View key="renderTopItemOfList2" style={{ backgroundColor: "white", marginTop: 50 }}>
        <View style={{ width: Dimen.screenWidth, backgroundColor: isHeaderSticked ? "white" : colors.transparent, marginTop: -50 }}>
          <CardPrices />
        </View>
        <View style={NoticeContainer}>
          <MIcon name="notification" size={Dimen.iconSize.sm} containerStyle={{ marginRight: 2 }} />
          {notifications != null && notifications.length > 0 && <RunningText
            speed={0.8}
            marqueeOnStart={true}
            consecutive={true}
            loop={true}
            delay={1000}
            datas={notiDisplay}
            onItemClicked={(id) => {
              console.log("onItemClicked: ", id)
              _props.navigation.navigate("NewsDetails", { postId: id })
            }}
            textStyle={RunningTextStyle}
            style={{ width: Dimen.screenWidth * 0.8 }}
          ></RunningText>}
        </View>
        {renderActionButton()}
      </View>
    )
  }

  const renderStickyLevel3 = () => {
    return (
      <View key="renderTopItemOfList">
        <BtnContactComp navigation={navigation} />
        <Text style={[TEXT_SECTION, MARGIN_DF_LEFT, MARGIN_DF_TOP]} weight="bold" tx="homePage.trend" />
        <View style={[ROW, MARGIN_DF]}>
          <View style={[CONTAINER_BORDER, FLEX, { padding: Dimen.padding.ssm * 1.5 }]}>
            <View style={[GREY_BORDER, FLEX, COLUMN, AI_CENTER, JC_CENTER]}>
              <Text style={TextDate} text={Helper.getCurrentDate()} />
              <Text numberOfLines={1} style={TextMonth}
                text={(AppStorage.appStorage.setting.language == 'EN') ? Helper.getCurrentMonthShortEng() : Helper.getCurrentMonthShort() + translate("common.month")} />
            </View>
          </View>
          <TouchableOpacity
            onPress={() => {
              if (!AppStorage.isNetworkConnected()) {
                return
              }
              AppStorage.setScreenNameNavigateAuto("MarketNews")
              AppStorage.setCurrentTrendScreenIndex(1)
              // navigation.navigate("Trend")
              navigation.navigate(
                "Trend",
                {
                  screen: "MarketNews"
                }
              )
            }}
            style={[CONTAINER_BORDER, ROW, AI_CENTER, FLEX_2, MARGIN_DF_LEFT, MARGIN_DF_RIGHT, { padding: Dimen.padding.ssm * 1.5 }]}>
            <MIcon name="quickNews" size={Dimen.iconSize.xl} />
            <View style={FLEX} />
            <View style={[COLUMN, AI_END, JC_CENTER]}>
              <Text style={[TEXT_SECTION, MARGIN_S_DF_LEFT]} tx="homePage.financialNews" />
              <Text style={[TEXT_SMALL, MARGIN_S_DF_LEFT, { opacity: totalMarketNews > 0 ? 1 : 0, color: "#7C7C7C", marginTop: -5 }]} text={totalMarketNews + translate("homePage.information")} size={Platform.OS === 'ios' ? 'sm' : 'xs'} />
            </View>
          </TouchableOpacity>
          <TouchableOpacity
            onPress={() => {
              if (!AppStorage.isNetworkConnected()) {
                return
              }
              AppStorage.setScreenNameNavigateAuto("Calendar")
              AppStorage.setCurrentTrendScreenIndex(2)
              // navigation.navigate("Trend")
              navigation.navigate(
                "Trend",
                {
                  screen: "Calendar"
                }
              )
            }}
            style={[CONTAINER_BORDER, ROW, AI_CENTER, FLEX_2, { padding: Dimen.padding.ssm * 1.5 }]}>
            <MIcon name="calendar" size={Dimen.iconSize.xl} />
            <View style={FLEX} />
            <View style={[COLUMN, AI_END, JC_CENTER]}>
              <Text style={[TEXT_SECTION, MARGIN_S_DF_LEFT]} tx="homePage.financialCalendar" />
              <Text style={[TEXT_SMALL, MARGIN_S_DF_LEFT, { opacity: totalcalendarEvent > 0 ? 1 : 0, color: "#7C7C7C", marginTop: -5 }]} text={totalcalendarEvent + translate("homePage.information")} />
            </View>
          </TouchableOpacity>
        </View>
      </View>
    )
  }

  return (
    <Screen
      preset="fixed"
      safeAreaEdges={["top", "bottom"]}
      backgroundColor="white"
      contentContainerStyle={{ position: "relative" }}
    >
      <Mheader navigator={navigation} />
      {
        posts.length > 2 &&
        <FlatList
          data={posts}
          // nestedScrollEnabled={true}
          scrollEnabled={mainScrollEnable}
          onRefresh={() => {
            setRefreshing(true)
            loadPosts()
            resetTimeout()
          }}
          onScroll={(event) => {
            if (event.nativeEvent.contentOffset.y > getFakeStickyHeight() + 20 && !isHeaderSticked) {
              setIsHeaderSticked(true)
            } else if (event.nativeEvent.contentOffset.y <= getFakeStickyHeight() && isHeaderSticked) {
              setIsHeaderSticked(false)
            }
          }}
          stickyHeaderIndices={[1]}
          refreshing={refreshing}
          keyExtractor={item => item.id.toString()}
          contentContainerStyle={{ paddingBottom: 100 }}
          renderItem={({ item, index }) => {
            if (index === 0) {
              return renderStickyLevel1()
            } else if (index === 1) {
              return renderStickyLevel2()
            } else if (index === 2) {
              return renderStickyLevel3()
            } else {
              return (
                <View style={{ width: Dimen.screenWidth, backgroundColor: "white" }}>
                  <CardNews
                    key={item.id}
                    onPress={() => {
                      _props.navigation.navigate("NewsDetails", { postId: item.id })
                    }}
                    data={item} />
                </View>
              )
            }
          }}
        />
      }
    </Screen>
  )
})