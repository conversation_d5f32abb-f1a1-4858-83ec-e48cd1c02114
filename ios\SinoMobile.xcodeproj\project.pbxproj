// !$*UTF8*$!
{
	archiveVersion = 1;
	classes = {
	};
	objectVersion = 54;
	objects = {

/* Begin PBXBuildFile section */
		00E356F31AD99517003FC87E /* SinoMobileTests.m in Sources */ = {isa = PBXBuildFile; fileRef = 00E356F21AD99517003FC87E /* SinoMobileTests.m */; };
		0C80B921A6F3F58F76C31292 /* (null) in Frameworks */ = {isa = PBXBuildFile; };
		13B07FBC1A68108700A75B9A /* AppDelegate.mm in Sources */ = {isa = PBXBuildFile; fileRef = 13B07FB01A68108700A75B9A /* AppDelegate.mm */; };
		13B07FBF1A68108700A75B9A /* Images.xcassets in Resources */ = {isa = PBXBuildFile; fileRef = 13B07FB51A68108700A75B9A /* Images.xcassets */; };
		13B07FC11A68108700A75B9A /* main.m in Sources */ = {isa = PBXBuildFile; fileRef = 13B07FB71A68108700A75B9A /* main.m */; };
		2215ACBC1E62C44583DB69A9 /* ExpoModulesProvider.swift in Sources */ = {isa = PBXBuildFile; fileRef = D5AFED2E09CC2E86D1338C0F /* ExpoModulesProvider.swift */; };
		2C40E7E52C2603F5005E9940 /* RCTAliyunPushExtensionModule.m in Sources */ = {isa = PBXBuildFile; fileRef = 2C40E7E32C2603F5005E9940 /* RCTAliyunPushExtensionModule.m */; };
		2C40E7E62C2603F5005E9940 /* RCTAliyunPushExtensionModule.m in Sources */ = {isa = PBXBuildFile; fileRef = 2C40E7E32C2603F5005E9940 /* RCTAliyunPushExtensionModule.m */; };
		400EB2C1E1807E6CBC68B7AB /* ExpoModulesProvider.swift in Sources */ = {isa = PBXBuildFile; fileRef = 951E59D51E504BC9C5F46169 /* ExpoModulesProvider.swift */; };
		468E75AF2BFE526100FA353B /* Launch Screen.storyboard in Resources */ = {isa = PBXBuildFile; fileRef = 468E75AE2BFE526100FA353B /* Launch Screen.storyboard */; };
		46F9F81C2C0F6BBA00A6CCA0 /* <EMAIL> in Resources */ = {isa = PBXBuildFile; fileRef = 46F9F8162C0F6BBA00A6CCA0 /* <EMAIL> */; };
		46F9F81D2C0F6BBA00A6CCA0 /* <EMAIL> in Resources */ = {isa = PBXBuildFile; fileRef = 46F9F8172C0F6BBA00A6CCA0 /* <EMAIL> */; };
		46F9F81E2C0F6BBA00A6CCA0 /* <EMAIL> in Resources */ = {isa = PBXBuildFile; fileRef = 46F9F8182C0F6BBA00A6CCA0 /* <EMAIL> */; };
		46F9F81F2C0F6BBA00A6CCA0 /* <EMAIL> in Resources */ = {isa = PBXBuildFile; fileRef = 46F9F8192C0F6BBA00A6CCA0 /* <EMAIL> */; };
		46F9F8202C0F6BBA00A6CCA0 /* <EMAIL> in Resources */ = {isa = PBXBuildFile; fileRef = 46F9F81A2C0F6BBA00A6CCA0 /* <EMAIL> */; };
		46F9F8212C0F6BBA00A6CCA0 /* <EMAIL> in Resources */ = {isa = PBXBuildFile; fileRef = 46F9F81B2C0F6BBA00A6CCA0 /* <EMAIL> */; };
		4980300817217093C784BC1D /* libPods-SinoMobile-SinoMobileTests.a in Frameworks */ = {isa = PBXBuildFile; fileRef = 5CA52CD1DDBED82ED61293D6 /* libPods-SinoMobile-SinoMobileTests.a */; };
		895BE754D47614BAB8D94E85 /* libPods-SinoMobile.a in Frameworks */ = {isa = PBXBuildFile; fileRef = 8867BE533A65F287ABA1757B /* libPods-SinoMobile.a */; };
		A6118E5C2C0858E3005A17E4 /* InfoPlist.strings in Resources */ = {isa = PBXBuildFile; fileRef = A6118E5E2C0858E3005A17E4 /* InfoPlist.strings */; };
		BE406F562862D73000A81E39 /* BootSplash.storyboard in Resources */ = {isa = PBXBuildFile; fileRef = BE406F552862D73000A81E39 /* BootSplash.storyboard */; };
/* End PBXBuildFile section */

/* Begin PBXContainerItemProxy section */
		00E356F41AD99517003FC87E /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = 83CBB9F71A601CBA00E9B192 /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = 13B07F861A680F5B00A75B9A;
			remoteInfo = SinoMobile;
		};
		46B287442C64B82000CE2ADD /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = 46B2873E2C64B81F00CE2ADD /* RNCNetInfo.xcodeproj */;
			proxyType = 2;
			remoteGlobalIDString = 134814201AA4EA6300B7C361;
			remoteInfo = RNCNetInfo;
		};
		46B287462C64B82000CE2ADD /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = 46B2873E2C64B81F00CE2ADD /* RNCNetInfo.xcodeproj */;
			proxyType = 2;
			remoteGlobalIDString = B5027B1B2237B30F00F1AABA;
			remoteInfo = "RNCNetInfo-tvOS";
		};
		46B287482C64B82000CE2ADD /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = 46B2873E2C64B81F00CE2ADD /* RNCNetInfo.xcodeproj */;
			proxyType = 2;
			remoteGlobalIDString = 3846DAC62403170500B4283E;
			remoteInfo = "RNCNetInfo-macOS";
		};
/* End PBXContainerItemProxy section */

/* Begin PBXFileReference section */
		00E356EE1AD99517003FC87E /* SinoMobileTests.xctest */ = {isa = PBXFileReference; explicitFileType = wrapper.cfbundle; includeInIndex = 0; path = SinoMobileTests.xctest; sourceTree = BUILT_PRODUCTS_DIR; };
		00E356F11AD99517003FC87E /* Info.plist */ = {isa = PBXFileReference; lastKnownFileType = text.plist.xml; path = Info.plist; sourceTree = "<group>"; };
		00E356F21AD99517003FC87E /* SinoMobileTests.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = SinoMobileTests.m; sourceTree = "<group>"; };
		13B07F961A680F5B00A75B9A /* SinoMobile.app */ = {isa = PBXFileReference; explicitFileType = wrapper.application; includeInIndex = 0; path = SinoMobile.app; sourceTree = BUILT_PRODUCTS_DIR; };
		13B07FAF1A68108700A75B9A /* AppDelegate.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; name = AppDelegate.h; path = SinoMobile/AppDelegate.h; sourceTree = "<group>"; };
		13B07FB01A68108700A75B9A /* AppDelegate.mm */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.cpp.objcpp; name = AppDelegate.mm; path = SinoMobile/AppDelegate.mm; sourceTree = "<group>"; };
		13B07FB51A68108700A75B9A /* Images.xcassets */ = {isa = PBXFileReference; lastKnownFileType = folder.assetcatalog; name = Images.xcassets; path = SinoMobile/Images.xcassets; sourceTree = "<group>"; };
		13B07FB61A68108700A75B9A /* Info.plist */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = text.plist.xml; name = Info.plist; path = SinoMobile/Info.plist; sourceTree = "<group>"; };
		13B07FB71A68108700A75B9A /* main.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; name = main.m; path = SinoMobile/main.m; sourceTree = "<group>"; };
		2C40E7E32C2603F5005E9940 /* RCTAliyunPushExtensionModule.m */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.objc; name = RCTAliyunPushExtensionModule.m; path = SinoMobile/RCTAliyunPushExtensionModule.m; sourceTree = "<group>"; };
		2C40E7E42C2603F5005E9940 /* RCTAliyunPushExtensionModule.h */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = sourcecode.c.h; name = RCTAliyunPushExtensionModule.h; path = SinoMobile/RCTAliyunPushExtensionModule.h; sourceTree = "<group>"; };
		2C6824062C26035600C94D4E /* SinoMobile.entitlements */ = {isa = PBXFileReference; lastKnownFileType = text.plist.entitlements; name = SinoMobile.entitlements; path = SinoMobile/SinoMobile.entitlements; sourceTree = "<group>"; };
		468E75AE2BFE526100FA353B /* Launch Screen.storyboard */ = {isa = PBXFileReference; lastKnownFileType = file.storyboard; path = "Launch Screen.storyboard"; sourceTree = "<group>"; };
		46B2873E2C64B81F00CE2ADD /* RNCNetInfo.xcodeproj */ = {isa = PBXFileReference; lastKnownFileType = "wrapper.pb-project"; name = RNCNetInfo.xcodeproj; path = "../node_modules/@react-native-community/netinfo/macos/RNCNetInfo.xcodeproj"; sourceTree = "<group>"; };
		46F9F8162C0F6BBA00A6CCA0 /* <EMAIL> */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = "<EMAIL>"; sourceTree = "<group>"; };
		46F9F8172C0F6BBA00A6CCA0 /* <EMAIL> */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = "<EMAIL>"; sourceTree = "<group>"; };
		46F9F8182C0F6BBA00A6CCA0 /* <EMAIL> */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = "<EMAIL>"; sourceTree = "<group>"; };
		46F9F8192C0F6BBA00A6CCA0 /* <EMAIL> */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = "<EMAIL>"; sourceTree = "<group>"; };
		46F9F81A2C0F6BBA00A6CCA0 /* <EMAIL> */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = "<EMAIL>"; sourceTree = "<group>"; };
		46F9F81B2C0F6BBA00A6CCA0 /* <EMAIL> */ = {isa = PBXFileReference; lastKnownFileType = image.png; path = "<EMAIL>"; sourceTree = "<group>"; };
		5CA52CD1DDBED82ED61293D6 /* libPods-SinoMobile-SinoMobileTests.a */ = {isa = PBXFileReference; explicitFileType = archive.ar; includeInIndex = 0; path = "libPods-SinoMobile-SinoMobileTests.a"; sourceTree = BUILT_PRODUCTS_DIR; };
		68D8E8233D07E7EEBD9CCA1A /* Pods-SinoMobile.debug.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = "Pods-SinoMobile.debug.xcconfig"; path = "Target Support Files/Pods-SinoMobile/Pods-SinoMobile.debug.xcconfig"; sourceTree = "<group>"; };
		6BDCDD8BA7F90B92FB9D86F5 /* Pods-SinoMobile-SinoMobileTests.debug.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = "Pods-SinoMobile-SinoMobileTests.debug.xcconfig"; path = "Target Support Files/Pods-SinoMobile-SinoMobileTests/Pods-SinoMobile-SinoMobileTests.debug.xcconfig"; sourceTree = "<group>"; };
		8867BE533A65F287ABA1757B /* libPods-SinoMobile.a */ = {isa = PBXFileReference; explicitFileType = archive.ar; includeInIndex = 0; path = "libPods-SinoMobile.a"; sourceTree = BUILT_PRODUCTS_DIR; };
		951E59D51E504BC9C5F46169 /* ExpoModulesProvider.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = ExpoModulesProvider.swift; path = "Pods/Target Support Files/Pods-SinoMobile/ExpoModulesProvider.swift"; sourceTree = "<group>"; };
		A321D02A2BFCA1108C4324C3 /* Pods-SinoMobile-SinoMobileTests.release.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = "Pods-SinoMobile-SinoMobileTests.release.xcconfig"; path = "Target Support Files/Pods-SinoMobile-SinoMobileTests/Pods-SinoMobile-SinoMobileTests.release.xcconfig"; sourceTree = "<group>"; };
		A6118E5D2C0858E3005A17E4 /* en */ = {isa = PBXFileReference; lastKnownFileType = text.plist.strings; name = en; path = en.lproj/InfoPlist.strings; sourceTree = "<group>"; };
		A6118E5F2C085955005A17E4 /* zh-Hant */ = {isa = PBXFileReference; lastKnownFileType = text.plist.strings; name = "zh-Hant"; path = "zh-Hant.lproj/InfoPlist.strings"; sourceTree = "<group>"; };
		A6118E602C08595C005A17E4 /* zh-HK */ = {isa = PBXFileReference; lastKnownFileType = text.plist.strings; name = "zh-HK"; path = "zh-HK.lproj/InfoPlist.strings"; sourceTree = "<group>"; };
		A6118E612C085961005A17E4 /* zh-Hans */ = {isa = PBXFileReference; lastKnownFileType = text.plist.strings; name = "zh-Hans"; path = "zh-Hans.lproj/InfoPlist.strings"; sourceTree = "<group>"; };
		BE406F552862D73000A81E39 /* BootSplash.storyboard */ = {isa = PBXFileReference; fileEncoding = 4; lastKnownFileType = file.storyboard; name = BootSplash.storyboard; path = SinoMobile/BootSplash.storyboard; sourceTree = "<group>"; };
		D5AFED2E09CC2E86D1338C0F /* ExpoModulesProvider.swift */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = sourcecode.swift; name = ExpoModulesProvider.swift; path = "Pods/Target Support Files/Pods-SinoMobile-SinoMobileTests/ExpoModulesProvider.swift"; sourceTree = "<group>"; };
		ED297162215061F000B7C4FE /* JavaScriptCore.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = JavaScriptCore.framework; path = System/Library/Frameworks/JavaScriptCore.framework; sourceTree = SDKROOT; };
		F861CF2B98B8F201DA6F8785 /* Pods-SinoMobile.release.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = "Pods-SinoMobile.release.xcconfig"; path = "Target Support Files/Pods-SinoMobile/Pods-SinoMobile.release.xcconfig"; sourceTree = "<group>"; };
/* End PBXFileReference section */

/* Begin PBXFrameworksBuildPhase section */
		00E356EB1AD99517003FC87E /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = **********;
			files = (
				4980300817217093C784BC1D /* libPods-SinoMobile-SinoMobileTests.a in Frameworks */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		13B07F8C1A680F5B00A75B9A /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = **********;
			files = (
				0C80B921A6F3F58F76C31292 /* (null) in Frameworks */,
				895BE754D47614BAB8D94E85 /* libPods-SinoMobile.a in Frameworks */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXFrameworksBuildPhase section */

/* Begin PBXGroup section */
		00E356EF1AD99517003FC87E /* SinoMobileTests */ = {
			isa = PBXGroup;
			children = (
				00E356F21AD99517003FC87E /* SinoMobileTests.m */,
				00E356F01AD99517003FC87E /* Supporting Files */,
			);
			path = SinoMobileTests;
			sourceTree = "<group>";
		};
		00E356F01AD99517003FC87E /* Supporting Files */ = {
			isa = PBXGroup;
			children = (
				00E356F11AD99517003FC87E /* Info.plist */,
			);
			name = "Supporting Files";
			sourceTree = "<group>";
		};
		13B07FAE1A68108700A75B9A /* SinoMobile */ = {
			isa = PBXGroup;
			children = (
				2C40E7E42C2603F5005E9940 /* RCTAliyunPushExtensionModule.h */,
				2C40E7E32C2603F5005E9940 /* RCTAliyunPushExtensionModule.m */,
				2C6824062C26035600C94D4E /* SinoMobile.entitlements */,
				46F9F80B2C0F67FE00A6CCA0 /* AlternateIcons */,
				13B07FAF1A68108700A75B9A /* AppDelegate.h */,
				13B07FB01A68108700A75B9A /* AppDelegate.mm */,
				13B07FB51A68108700A75B9A /* Images.xcassets */,
				13B07FB61A68108700A75B9A /* Info.plist */,
				BE406F552862D73000A81E39 /* BootSplash.storyboard */,
				13B07FB71A68108700A75B9A /* main.m */,
				468E75AE2BFE526100FA353B /* Launch Screen.storyboard */,
				A6118E5E2C0858E3005A17E4 /* InfoPlist.strings */,
			);
			name = SinoMobile;
			sourceTree = "<group>";
		};
		2D16E6871FA4F8E400B85C8A /* Frameworks */ = {
			isa = PBXGroup;
			children = (
				ED297162215061F000B7C4FE /* JavaScriptCore.framework */,
				8867BE533A65F287ABA1757B /* libPods-SinoMobile.a */,
				5CA52CD1DDBED82ED61293D6 /* libPods-SinoMobile-SinoMobileTests.a */,
			);
			name = Frameworks;
			sourceTree = "<group>";
		};
		46B2873F2C64B81F00CE2ADD /* Products */ = {
			isa = PBXGroup;
			children = (
				46B287452C64B82000CE2ADD /* libRNCNetInfo.a */,
				46B287472C64B82000CE2ADD /* libRNCNetInfo-tvOS.a */,
				46B287492C64B82000CE2ADD /* libRNCNetInfo-macOS.a */,
			);
			name = Products;
			sourceTree = "<group>";
		};
		46F9F80B2C0F67FE00A6CCA0 /* AlternateIcons */ = {
			isa = PBXGroup;
			children = (
				46F9F8162C0F6BBA00A6CCA0 /* <EMAIL> */,
				46F9F8172C0F6BBA00A6CCA0 /* <EMAIL> */,
				46F9F8182C0F6BBA00A6CCA0 /* <EMAIL> */,
				46F9F81A2C0F6BBA00A6CCA0 /* <EMAIL> */,
				46F9F81B2C0F6BBA00A6CCA0 /* <EMAIL> */,
				46F9F8192C0F6BBA00A6CCA0 /* <EMAIL> */,
			);
			path = AlternateIcons;
			sourceTree = "<group>";
		};
		547A8E9E9663ECB3355FB756 /* ExpoModulesProviders */ = {
			isa = PBXGroup;
			children = (
				6289853FD5D818F15D17248E /* SinoMobile */,
				E4DC8A9EFE6F9C5F90DD96E4 /* SinoMobileTests */,
			);
			name = ExpoModulesProviders;
			sourceTree = "<group>";
		};
		6289853FD5D818F15D17248E /* SinoMobile */ = {
			isa = PBXGroup;
			children = (
				951E59D51E504BC9C5F46169 /* ExpoModulesProvider.swift */,
			);
			name = SinoMobile;
			sourceTree = "<group>";
		};
		832341AE1AAA6A7D00B99B32 /* Libraries */ = {
			isa = PBXGroup;
			children = (
				46B2873E2C64B81F00CE2ADD /* RNCNetInfo.xcodeproj */,
			);
			name = Libraries;
			sourceTree = "<group>";
		};
		83CBB9F61A601CBA00E9B192 = {
			isa = PBXGroup;
			children = (
				13B07FAE1A68108700A75B9A /* SinoMobile */,
				832341AE1AAA6A7D00B99B32 /* Libraries */,
				00E356EF1AD99517003FC87E /* SinoMobileTests */,
				83CBBA001A601CBA00E9B192 /* Products */,
				2D16E6871FA4F8E400B85C8A /* Frameworks */,
				BBD78D7AC51CEA395F1C20DB /* Pods */,
				547A8E9E9663ECB3355FB756 /* ExpoModulesProviders */,
			);
			indentWidth = 2;
			sourceTree = "<group>";
			tabWidth = 2;
			usesTabs = 0;
		};
		83CBBA001A601CBA00E9B192 /* Products */ = {
			isa = PBXGroup;
			children = (
				13B07F961A680F5B00A75B9A /* SinoMobile.app */,
				00E356EE1AD99517003FC87E /* SinoMobileTests.xctest */,
			);
			name = Products;
			sourceTree = "<group>";
		};
		BBD78D7AC51CEA395F1C20DB /* Pods */ = {
			isa = PBXGroup;
			children = (
				68D8E8233D07E7EEBD9CCA1A /* Pods-SinoMobile.debug.xcconfig */,
				F861CF2B98B8F201DA6F8785 /* Pods-SinoMobile.release.xcconfig */,
				6BDCDD8BA7F90B92FB9D86F5 /* Pods-SinoMobile-SinoMobileTests.debug.xcconfig */,
				A321D02A2BFCA1108C4324C3 /* Pods-SinoMobile-SinoMobileTests.release.xcconfig */,
			);
			path = Pods;
			sourceTree = "<group>";
		};
		E4DC8A9EFE6F9C5F90DD96E4 /* SinoMobileTests */ = {
			isa = PBXGroup;
			children = (
				D5AFED2E09CC2E86D1338C0F /* ExpoModulesProvider.swift */,
			);
			name = SinoMobileTests;
			sourceTree = "<group>";
		};
/* End PBXGroup section */

/* Begin PBXNativeTarget section */
		00E356ED1AD99517003FC87E /* SinoMobileTests */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 00E357021AD99517003FC87E /* Build configuration list for PBXNativeTarget "SinoMobileTests" */;
			buildPhases = (
				6852658CE0CD9B159E4EFEA8 /* [CP] Check Pods Manifest.lock */,
				00E356EA1AD99517003FC87E /* Sources */,
				00E356EB1AD99517003FC87E /* Frameworks */,
				00E356EC1AD99517003FC87E /* Resources */,
				FDD702488047F07642F81BB7 /* [CP] Embed Pods Frameworks */,
				86CF7ACFE92A60C68905ACAD /* [CP] Copy Pods Resources */,
			);
			buildRules = (
			);
			dependencies = (
				00E356F51AD99517003FC87E /* PBXTargetDependency */,
			);
			name = SinoMobileTests;
			productName = SinoMobileTests;
			productReference = 00E356EE1AD99517003FC87E /* SinoMobileTests.xctest */;
			productType = "com.apple.product-type.bundle.unit-test";
		};
		13B07F861A680F5B00A75B9A /* SinoMobile */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 13B07F931A680F5B00A75B9A /* Build configuration list for PBXNativeTarget "SinoMobile" */;
			buildPhases = (
				693DAE50E4A11922EFE7EE47 /* [CP] Check Pods Manifest.lock */,
				FD10A7F022414F080027D42C /* Start Packager */,
				13B07F871A680F5B00A75B9A /* Sources */,
				13B07F8C1A680F5B00A75B9A /* Frameworks */,
				13B07F8E1A680F5B00A75B9A /* Resources */,
				00DD1BFF1BD5951E006B06BC /* Bundle React Native code and images */,
				36FED7174CCAE8DB08FDA239 /* [CP] Embed Pods Frameworks */,
				CEA62064920A6A14D1219417 /* [CP] Copy Pods Resources */,
			);
			buildRules = (
			);
			dependencies = (
			);
			name = SinoMobile;
			productName = SinoMobile;
			productReference = 13B07F961A680F5B00A75B9A /* SinoMobile.app */;
			productType = "com.apple.product-type.application";
		};
/* End PBXNativeTarget section */

/* Begin PBXProject section */
		83CBB9F71A601CBA00E9B192 /* Project object */ = {
			isa = PBXProject;
			attributes = {
				LastUpgradeCheck = 1340;
				TargetAttributes = {
					00E356ED1AD99517003FC87E = {
						CreatedOnToolsVersion = 6.2;
						TestTargetID = 13B07F861A680F5B00A75B9A;
					};
					13B07F861A680F5B00A75B9A = {
						LastSwiftMigration = 1120;
					};
				};
			};
			buildConfigurationList = 83CBB9FA1A601CBA00E9B192 /* Build configuration list for PBXProject "SinoMobile" */;
			compatibilityVersion = "Xcode 12.0";
			developmentRegion = en;
			hasScannedForEncodings = 0;
			knownRegions = (
				en,
				Base,
				"zh-Hant",
				"zh-HK",
				"zh-Hans",
			);
			mainGroup = 83CBB9F61A601CBA00E9B192;
			productRefGroup = 83CBBA001A601CBA00E9B192 /* Products */;
			projectDirPath = "";
			projectReferences = (
				{
					ProductGroup = 46B2873F2C64B81F00CE2ADD /* Products */;
					ProjectRef = 46B2873E2C64B81F00CE2ADD /* RNCNetInfo.xcodeproj */;
				},
			);
			projectRoot = "";
			targets = (
				13B07F861A680F5B00A75B9A /* SinoMobile */,
				00E356ED1AD99517003FC87E /* SinoMobileTests */,
			);
		};
/* End PBXProject section */

/* Begin PBXReferenceProxy section */
		46B287452C64B82000CE2ADD /* libRNCNetInfo.a */ = {
			isa = PBXReferenceProxy;
			fileType = archive.ar;
			path = libRNCNetInfo.a;
			remoteRef = 46B287442C64B82000CE2ADD /* PBXContainerItemProxy */;
			sourceTree = BUILT_PRODUCTS_DIR;
		};
		46B287472C64B82000CE2ADD /* libRNCNetInfo-tvOS.a */ = {
			isa = PBXReferenceProxy;
			fileType = archive.ar;
			path = "libRNCNetInfo-tvOS.a";
			remoteRef = 46B287462C64B82000CE2ADD /* PBXContainerItemProxy */;
			sourceTree = BUILT_PRODUCTS_DIR;
		};
		46B287492C64B82000CE2ADD /* libRNCNetInfo-macOS.a */ = {
			isa = PBXReferenceProxy;
			fileType = archive.ar;
			path = "libRNCNetInfo-macOS.a";
			remoteRef = 46B287482C64B82000CE2ADD /* PBXContainerItemProxy */;
			sourceTree = BUILT_PRODUCTS_DIR;
		};
/* End PBXReferenceProxy section */

/* Begin PBXResourcesBuildPhase section */
		00E356EC1AD99517003FC87E /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = **********;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		13B07F8E1A680F5B00A75B9A /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = **********;
			files = (
				46F9F8202C0F6BBA00A6CCA0 /* <EMAIL> in Resources */,
				468E75AF2BFE526100FA353B /* Launch Screen.storyboard in Resources */,
				A6118E5C2C0858E3005A17E4 /* InfoPlist.strings in Resources */,
				46F9F8212C0F6BBA00A6CCA0 /* <EMAIL> in Resources */,
				46F9F81F2C0F6BBA00A6CCA0 /* <EMAIL> in Resources */,
				46F9F81E2C0F6BBA00A6CCA0 /* <EMAIL> in Resources */,
				BE406F562862D73000A81E39 /* BootSplash.storyboard in Resources */,
				46F9F81D2C0F6BBA00A6CCA0 /* <EMAIL> in Resources */,
				46F9F81C2C0F6BBA00A6CCA0 /* <EMAIL> in Resources */,
				13B07FBF1A68108700A75B9A /* Images.xcassets in Resources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXResourcesBuildPhase section */

/* Begin PBXShellScriptBuildPhase section */
		00DD1BFF1BD5951E006B06BC /* Bundle React Native code and images */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = **********;
			files = (
			);
			inputPaths = (
				"$(SRCROOT)/.xcode.env.local",
				"$(SRCROOT)/.xcode.env",
			);
			name = "Bundle React Native code and images";
			outputPaths = (
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "set -e\n\nWITH_ENVIRONMENT=\"../node_modules/react-native/scripts/xcode/with-environment.sh\"\nREACT_NATIVE_XCODE=\"../node_modules/react-native/scripts/react-native-xcode.sh\"\n\n/bin/sh -c \"$WITH_ENVIRONMENT $REACT_NATIVE_XCODE\"\n";
		};
		36FED7174CCAE8DB08FDA239 /* [CP] Embed Pods Frameworks */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = **********;
			files = (
			);
			inputFileListPaths = (
				"${PODS_ROOT}/Target Support Files/Pods-SinoMobile/Pods-SinoMobile-frameworks-${CONFIGURATION}-input-files.xcfilelist",
			);
			name = "[CP] Embed Pods Frameworks";
			outputFileListPaths = (
				"${PODS_ROOT}/Target Support Files/Pods-SinoMobile/Pods-SinoMobile-frameworks-${CONFIGURATION}-output-files.xcfilelist",
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "\"${PODS_ROOT}/Target Support Files/Pods-SinoMobile/Pods-SinoMobile-frameworks.sh\"\n";
			showEnvVarsInLog = 0;
		};
		6852658CE0CD9B159E4EFEA8 /* [CP] Check Pods Manifest.lock */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = **********;
			files = (
			);
			inputFileListPaths = (
			);
			inputPaths = (
				"${PODS_PODFILE_DIR_PATH}/Podfile.lock",
				"${PODS_ROOT}/Manifest.lock",
			);
			name = "[CP] Check Pods Manifest.lock";
			outputFileListPaths = (
			);
			outputPaths = (
				"$(DERIVED_FILE_DIR)/Pods-SinoMobile-SinoMobileTests-checkManifestLockResult.txt",
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "diff \"${PODS_PODFILE_DIR_PATH}/Podfile.lock\" \"${PODS_ROOT}/Manifest.lock\" > /dev/null\nif [ $? != 0 ] ; then\n    # print error to STDERR\n    echo \"error: The sandbox is not in sync with the Podfile.lock. Run 'pod install' or update your CocoaPods installation.\" >&2\n    exit 1\nfi\n# This output is used by Xcode 'outputs' to avoid re-running this script phase.\necho \"SUCCESS\" > \"${SCRIPT_OUTPUT_FILE_0}\"\n";
			showEnvVarsInLog = 0;
		};
		693DAE50E4A11922EFE7EE47 /* [CP] Check Pods Manifest.lock */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = **********;
			files = (
			);
			inputFileListPaths = (
			);
			inputPaths = (
				"${PODS_PODFILE_DIR_PATH}/Podfile.lock",
				"${PODS_ROOT}/Manifest.lock",
			);
			name = "[CP] Check Pods Manifest.lock";
			outputFileListPaths = (
			);
			outputPaths = (
				"$(DERIVED_FILE_DIR)/Pods-SinoMobile-checkManifestLockResult.txt",
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "diff \"${PODS_PODFILE_DIR_PATH}/Podfile.lock\" \"${PODS_ROOT}/Manifest.lock\" > /dev/null\nif [ $? != 0 ] ; then\n    # print error to STDERR\n    echo \"error: The sandbox is not in sync with the Podfile.lock. Run 'pod install' or update your CocoaPods installation.\" >&2\n    exit 1\nfi\n# This output is used by Xcode 'outputs' to avoid re-running this script phase.\necho \"SUCCESS\" > \"${SCRIPT_OUTPUT_FILE_0}\"\n";
			showEnvVarsInLog = 0;
		};
		86CF7ACFE92A60C68905ACAD /* [CP] Copy Pods Resources */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = **********;
			files = (
			);
			inputFileListPaths = (
				"${PODS_ROOT}/Target Support Files/Pods-SinoMobile-SinoMobileTests/Pods-SinoMobile-SinoMobileTests-resources-${CONFIGURATION}-input-files.xcfilelist",
			);
			name = "[CP] Copy Pods Resources";
			outputFileListPaths = (
				"${PODS_ROOT}/Target Support Files/Pods-SinoMobile-SinoMobileTests/Pods-SinoMobile-SinoMobileTests-resources-${CONFIGURATION}-output-files.xcfilelist",
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "\"${PODS_ROOT}/Target Support Files/Pods-SinoMobile-SinoMobileTests/Pods-SinoMobile-SinoMobileTests-resources.sh\"\n";
			showEnvVarsInLog = 0;
		};
		CEA62064920A6A14D1219417 /* [CP] Copy Pods Resources */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = **********;
			files = (
			);
			inputFileListPaths = (
				"${PODS_ROOT}/Target Support Files/Pods-SinoMobile/Pods-SinoMobile-resources-${CONFIGURATION}-input-files.xcfilelist",
			);
			name = "[CP] Copy Pods Resources";
			outputFileListPaths = (
				"${PODS_ROOT}/Target Support Files/Pods-SinoMobile/Pods-SinoMobile-resources-${CONFIGURATION}-output-files.xcfilelist",
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "\"${PODS_ROOT}/Target Support Files/Pods-SinoMobile/Pods-SinoMobile-resources.sh\"\n";
			showEnvVarsInLog = 0;
		};
		FD10A7F022414F080027D42C /* Start Packager */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = **********;
			files = (
			);
			inputFileListPaths = (
			);
			inputPaths = (
			);
			name = "Start Packager";
			outputFileListPaths = (
			);
			outputPaths = (
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "export RCT_METRO_PORT=\"${RCT_METRO_PORT:=8081}\"\necho \"export RCT_METRO_PORT=${RCT_METRO_PORT}\" > \"${SRCROOT}/../node_modules/react-native/scripts/.packager.env\"\nif [ -z \"${RCT_NO_LAUNCH_PACKAGER+xxx}\" ] ; then\n  if nc -w 5 -z localhost ${RCT_METRO_PORT} ; then\n    if ! curl -s \"http://localhost:${RCT_METRO_PORT}/status\" | grep -q \"packager-status:running\" ; then\n      echo \"Port ${RCT_METRO_PORT} already in use, packager is either not running or not running correctly\"\n      exit 2\n    fi\n  else\n    open \"$SRCROOT/../node_modules/react-native/scripts/launchPackager.command\" || echo \"Can't start packager automatically\"\n  fi\nfi\n";
			showEnvVarsInLog = 0;
		};
		FDD702488047F07642F81BB7 /* [CP] Embed Pods Frameworks */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = **********;
			files = (
			);
			inputFileListPaths = (
				"${PODS_ROOT}/Target Support Files/Pods-SinoMobile-SinoMobileTests/Pods-SinoMobile-SinoMobileTests-frameworks-${CONFIGURATION}-input-files.xcfilelist",
			);
			name = "[CP] Embed Pods Frameworks";
			outputFileListPaths = (
				"${PODS_ROOT}/Target Support Files/Pods-SinoMobile-SinoMobileTests/Pods-SinoMobile-SinoMobileTests-frameworks-${CONFIGURATION}-output-files.xcfilelist",
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "\"${PODS_ROOT}/Target Support Files/Pods-SinoMobile-SinoMobileTests/Pods-SinoMobile-SinoMobileTests-frameworks.sh\"\n";
			showEnvVarsInLog = 0;
		};
/* End PBXShellScriptBuildPhase section */

/* Begin PBXSourcesBuildPhase section */
		00E356EA1AD99517003FC87E /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = **********;
			files = (
				2C40E7E62C2603F5005E9940 /* RCTAliyunPushExtensionModule.m in Sources */,
				00E356F31AD99517003FC87E /* SinoMobileTests.m in Sources */,
				2215ACBC1E62C44583DB69A9 /* ExpoModulesProvider.swift in Sources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		13B07F871A680F5B00A75B9A /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = **********;
			files = (
				13B07FBC1A68108700A75B9A /* AppDelegate.mm in Sources */,
				13B07FC11A68108700A75B9A /* main.m in Sources */,
				400EB2C1E1807E6CBC68B7AB /* ExpoModulesProvider.swift in Sources */,
				2C40E7E52C2603F5005E9940 /* RCTAliyunPushExtensionModule.m in Sources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXSourcesBuildPhase section */

/* Begin PBXTargetDependency section */
		00E356F51AD99517003FC87E /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			target = 13B07F861A680F5B00A75B9A /* SinoMobile */;
			targetProxy = 00E356F41AD99517003FC87E /* PBXContainerItemProxy */;
		};
/* End PBXTargetDependency section */

/* Begin PBXVariantGroup section */
		A6118E5E2C0858E3005A17E4 /* InfoPlist.strings */ = {
			isa = PBXVariantGroup;
			children = (
				A6118E5D2C0858E3005A17E4 /* en */,
				A6118E5F2C085955005A17E4 /* zh-Hant */,
				A6118E602C08595C005A17E4 /* zh-HK */,
				A6118E612C085961005A17E4 /* zh-Hans */,
			);
			name = InfoPlist.strings;
			sourceTree = "<group>";
		};
/* End PBXVariantGroup section */

/* Begin XCBuildConfiguration section */
		00E356F61AD99517003FC87E /* Debug */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 6BDCDD8BA7F90B92FB9D86F5 /* Pods-SinoMobile-SinoMobileTests.debug.xcconfig */;
			buildSettings = {
				ALWAYS_EMBED_SWIFT_STANDARD_LIBRARIES = YES;
				BUNDLE_LOADER = "$(TEST_HOST)";
				GCC_PREPROCESSOR_DEFINITIONS = (
					"DEBUG=1",
					"$(inherited)",
				);
				INFOPLIST_FILE = SinoMobileTests/Info.plist;
				IPHONEOS_DEPLOYMENT_TARGET = 13.0;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
					"@loader_path/Frameworks",
				);
				OTHER_LDFLAGS = (
					"-ObjC",
					"-lc++",
					"$(inherited)",
				);
				OTHER_SWIFT_FLAGS = "$(inherited) -D EXPO_CONFIGURATION_DEBUG";
				PRODUCT_BUNDLE_IDENTIFIER = com.sinosound.mobile2;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SWIFT_VERSION = 5.0;
				TEST_HOST = "$(BUILT_PRODUCTS_DIR)/SinoMobile.app/SinoMobile";
			};
			name = Debug;
		};
		00E356F71AD99517003FC87E /* Release */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = A321D02A2BFCA1108C4324C3 /* Pods-SinoMobile-SinoMobileTests.release.xcconfig */;
			buildSettings = {
				ALWAYS_EMBED_SWIFT_STANDARD_LIBRARIES = YES;
				BUNDLE_LOADER = "$(TEST_HOST)";
				COPY_PHASE_STRIP = NO;
				INFOPLIST_FILE = SinoMobileTests/Info.plist;
				IPHONEOS_DEPLOYMENT_TARGET = 13.0;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
					"@loader_path/Frameworks",
				);
				OTHER_LDFLAGS = (
					"-ObjC",
					"-lc++",
					"$(inherited)",
				);
				OTHER_SWIFT_FLAGS = "$(inherited) -D EXPO_CONFIGURATION_RELEASE";
				PRODUCT_BUNDLE_IDENTIFIER = com.sinosound.mobile2;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SWIFT_VERSION = 5.0;
				TEST_HOST = "$(BUILT_PRODUCTS_DIR)/SinoMobile.app/SinoMobile";
			};
			name = Release;
		};
		13B07F941A680F5B00A75B9A /* Debug */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 68D8E8233D07E7EEBD9CCA1A /* Pods-SinoMobile.debug.xcconfig */;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				ASSETCATALOG_COMPILER_INCLUDE_ALL_APPICON_ASSETS = NO;
				CLANG_ENABLE_MODULES = YES;
				CODE_SIGN_ENTITLEMENTS = SinoMobile/SinoMobile.entitlements;
				CODE_SIGN_IDENTITY = "Apple Development";
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_TEAM = 4LB3S7KZQ6;
				ENABLE_BITCODE = NO;
				"EXCLUDED_ARCHS[sdk=iphonesimulator*]" = "";
				INFOPLIST_FILE = SinoMobile/Info.plist;
				INFOPLIST_KEY_CFBundleDisplayName = SinoMobile;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
				);
				OTHER_LDFLAGS = (
					"$(inherited)",
					"-ObjC",
					"-lc++",
				);
				OTHER_SWIFT_FLAGS = "$(inherited) -D EXPO_CONFIGURATION_DEBUG";
				PRODUCT_BUNDLE_IDENTIFIER = com.sinosound.mobile2;
				PRODUCT_NAME = SinoMobile;
				PROVISIONING_PROFILE_SPECIFIER = "";
				SWIFT_OPTIMIZATION_LEVEL = "-Onone";
				SWIFT_VERSION = 5.0;
				VERSIONING_SYSTEM = "apple-generic";
			};
			name = Debug;
		};
		13B07F951A680F5B00A75B9A /* Release */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = F861CF2B98B8F201DA6F8785 /* Pods-SinoMobile.release.xcconfig */;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				ASSETCATALOG_COMPILER_INCLUDE_ALL_APPICON_ASSETS = NO;
				CLANG_ENABLE_MODULES = YES;
				CODE_SIGN_ENTITLEMENTS = SinoMobile/SinoMobile.entitlements;
				CODE_SIGN_IDENTITY = "Apple Development";
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_TEAM = 4LB3S7KZQ6;
				"EXCLUDED_ARCHS[sdk=iphonesimulator*]" = "";
				INFOPLIST_FILE = SinoMobile/Info.plist;
				INFOPLIST_KEY_CFBundleDisplayName = SinoMobile;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
				);
				OTHER_LDFLAGS = (
					"$(inherited)",
					"-ObjC",
					"-lc++",
				);
				OTHER_SWIFT_FLAGS = "$(inherited) -D EXPO_CONFIGURATION_RELEASE";
				PRODUCT_BUNDLE_IDENTIFIER = com.sinosound.mobile2;
				PRODUCT_NAME = SinoMobile;
				PROVISIONING_PROFILE_SPECIFIER = "";
				SWIFT_VERSION = 5.0;
				VERSIONING_SYSTEM = "apple-generic";
			};
			name = Release;
		};
		83CBBA201A601CBA00E9B192 /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				CLANG_ANALYZER_LOCALIZABILITY_NONLOCALIZED = YES;
				CLANG_CXX_LANGUAGE_STANDARD = "c++17";
				CLANG_CXX_LIBRARY = "libc++";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				"CODE_SIGN_IDENTITY[sdk=iphoneos*]" = "iPhone Developer";
				COPY_PHASE_STRIP = NO;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				ENABLE_TESTABILITY = YES;
				"EXCLUDED_ARCHS[sdk=iphonesimulator*]" = i386;
				GCC_C_LANGUAGE_STANDARD = gnu99;
				GCC_DYNAMIC_NO_PIC = NO;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_OPTIMIZATION_LEVEL = 0;
				GCC_PREPROCESSOR_DEFINITIONS = (
					"DEBUG=1",
					"$(inherited)",
				);
				GCC_SYMBOLS_PRIVATE_EXTERN = NO;
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 13.0;
				LD_RUNPATH_SEARCH_PATHS = (
					/usr/lib/swift,
					"$(inherited)",
				);
				LIBRARY_SEARCH_PATHS = (
					"\"$(SDKROOT)/usr/lib/swift\"",
					"\"$(TOOLCHAIN_DIR)/usr/lib/swift/$(PLATFORM_NAME)\"",
					"\"$(inherited)\"",
				);
				MTL_ENABLE_DEBUG_INFO = YES;
				ONLY_ACTIVE_ARCH = YES;
				OTHER_CPLUSPLUSFLAGS = (
					"$(OTHER_CFLAGS)",
					"-DFOLLY_NO_CONFIG",
					"-DFOLLY_MOBILE=1",
					"-DFOLLY_USE_LIBCPP=1",
				);
				REACT_NATIVE_PATH = "${PODS_ROOT}/../../node_modules/react-native";
				SDKROOT = iphoneos;
			};
			name = Debug;
		};
		83CBBA211A601CBA00E9B192 /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				CLANG_ANALYZER_LOCALIZABILITY_NONLOCALIZED = YES;
				CLANG_CXX_LANGUAGE_STANDARD = "c++17";
				CLANG_CXX_LIBRARY = "libc++";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				"CODE_SIGN_IDENTITY[sdk=iphoneos*]" = "iPhone Developer";
				COPY_PHASE_STRIP = YES;
				ENABLE_NS_ASSERTIONS = NO;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				"EXCLUDED_ARCHS[sdk=iphonesimulator*]" = i386;
				GCC_C_LANGUAGE_STANDARD = gnu99;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 13.0;
				LD_RUNPATH_SEARCH_PATHS = (
					/usr/lib/swift,
					"$(inherited)",
				);
				LIBRARY_SEARCH_PATHS = (
					"\"$(SDKROOT)/usr/lib/swift\"",
					"\"$(TOOLCHAIN_DIR)/usr/lib/swift/$(PLATFORM_NAME)\"",
					"\"$(inherited)\"",
				);
				MTL_ENABLE_DEBUG_INFO = NO;
				OTHER_CPLUSPLUSFLAGS = (
					"$(OTHER_CFLAGS)",
					"-DFOLLY_NO_CONFIG",
					"-DFOLLY_MOBILE=1",
					"-DFOLLY_USE_LIBCPP=1",
				);
				REACT_NATIVE_PATH = "${PODS_ROOT}/../../node_modules/react-native";
				SDKROOT = iphoneos;
				SWIFT_COMPILATION_MODE = wholemodule;
				VALIDATE_PRODUCT = YES;
			};
			name = Release;
		};
/* End XCBuildConfiguration section */

/* Begin XCConfigurationList section */
		00E357021AD99517003FC87E /* Build configuration list for PBXNativeTarget "SinoMobileTests" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				00E356F61AD99517003FC87E /* Debug */,
				00E356F71AD99517003FC87E /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		13B07F931A680F5B00A75B9A /* Build configuration list for PBXNativeTarget "SinoMobile" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				13B07F941A680F5B00A75B9A /* Debug */,
				13B07F951A680F5B00A75B9A /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		83CBB9FA1A601CBA00E9B192 /* Build configuration list for PBXProject "SinoMobile" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				83CBBA201A601CBA00E9B192 /* Debug */,
				83CBBA211A601CBA00E9B192 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
/* End XCConfigurationList section */
	};
	rootObject = 83CBB9F71A601CBA00E9B192 /* Project object */;
}
